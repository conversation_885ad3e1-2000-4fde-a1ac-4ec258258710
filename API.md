# DailySync API Documentation

## Base URL
```
Production: https://your-api-domain.com
Development: http://localhost:3001
```

## Authentication

All API endpoints (except webhooks) require authentication using Clerk JWT tokens.

### Headers
```
Authorization: Bearer <clerk_jwt_token>
Content-Type: application/json
```

## API Endpoints

### Users

#### Get Current User Profile
```http
GET /users/me
```

**Response:**
```json
{
  "id": "user_123",
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "role": "USER",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### Get All Users (Admin Only)
```http
GET /users
```

### Daily Updates

#### Create Daily Update
```http
POST /updates
```

**Request Body:**
```json
{
  "date": "2024-01-15",
  "tickets": 12,
  "chats": 8,
  "issues": 5,
  "reviews": 2,
  "metadata": {}
}
```

**Response:**
```json
{
  "id": "update_123",
  "userId": "user_123",
  "date": "2024-01-15",
  "tickets": 12,
  "chats": 8,
  "issues": 5,
  "reviews": 2,
  "createdAt": "2024-01-15T10:30:00Z",
  "user": {
    "id": "user_123",
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

#### Get User Updates
```http
GET /updates/my-updates?startDate=2024-01-01&endDate=2024-01-31
```

#### Get User Statistics
```http
GET /updates/my-stats?startDate=2024-01-01&endDate=2024-01-31
```

**Response:**
```json
{
  "updates": [...],
  "totals": {
    "tickets": 120,
    "chats": 80,
    "issues": 50,
    "reviews": 20
  },
  "count": 10
}
```

### Meetings

#### Get User Meetings
```http
GET /meetings/my-meetings?startDate=2024-01-01&endDate=2024-01-31
```

#### Get Meeting Statistics
```http
GET /meetings/my-stats?startDate=2024-01-01&endDate=2024-01-31
```

**Response:**
```json
{
  "meetings": [...],
  "totalMeetings": 15,
  "totalDuration": 675,
  "averageDuration": 45
}
```

### Analytics

#### Get User Dashboard
```http
GET /analytics/dashboard?startDate=2024-01-01&endDate=2024-01-31
```

**Response:**
```json
{
  "totals": {
    "tickets": 120,
    "chats": 80,
    "issues": 50,
    "reviews": 20
  },
  "meetingStats": {
    "totalMeetings": 15,
    "totalDuration": 675
  },
  "dailyBreakdown": [
    {
      "date": "2024-01-15",
      "tickets": 12,
      "chats": 8,
      "issues": 5,
      "reviews": 2,
      "meetings": 2,
      "meetingDuration": 90
    }
  ],
  "recentUpdates": [...],
  "recentMeetings": [...]
}
```

#### Get Team Dashboard (Admin Only)
```http
GET /analytics/team?startDate=2024-01-01&endDate=2024-01-31
```

**Response:**
```json
{
  "teamTotals": {
    "tickets": 1200,
    "chats": 800,
    "issues": 500,
    "reviews": 200
  },
  "leaderboards": {
    "topPerformers": [...],
    "topTicketHandlers": [...],
    "topChatHandlers": [...],
    "topIssueResolvers": [...],
    "topReviewers": [...]
  },
  "teamTrends": [...],
  "totalUsers": 25,
  "totalMeetings": 150
}
```

### Slack Integration

#### Get Slack OAuth URL
```http
GET /slack/oauth-url
```

**Response:**
```json
{
  "url": "https://slack.com/oauth/v2/authorize?client_id=..."
}
```

#### Check Slack Connection Status
```http
GET /slack/status
```

**Response:**
```json
{
  "connected": true
}
```

#### Disconnect Slack
```http
DELETE /slack/disconnect
```

#### Test Slack Connection
```http
POST /slack/test
```

### Webhooks

#### Meeting Webhook
```http
POST /webhook/meeting
```

**Request Body:**
```json
{
  "booking": {
    "id": 235,
    "host_user_id": "18",
    "start_time": "2025-05-30 14:00:00",
    "end_time": "2025-05-30 14:45:00",
    "slot_minutes": "45",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "status": "scheduled",
    "person_time_zone": "UTC",
    "location_details": {
      "type": "google_meet",
      "online_platform_link": "https://meet.google.com/abc-def-ghi"
    }
  },
  "calendar_event": {
    "title": "Client Meeting",
    "duration": "45"
  }
}
```

**Response:**
```json
{
  "success": true,
  "meetingId": "meeting_123",
  "message": "Meeting processed successfully"
}
```

## Error Responses

### Standard Error Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (e.g., duplicate daily update)
- `500` - Internal Server Error

## Rate Limiting

- General API: 100 requests per minute per user
- Slack endpoints: 10 requests per minute per user
- Webhook endpoints: 1000 requests per minute (no authentication required)

## Data Validation

### Daily Update Validation
- `date`: Required, valid date string (YYYY-MM-DD)
- `tickets`: Required, non-negative integer
- `chats`: Required, non-negative integer
- `issues`: Required, non-negative integer
- `reviews`: Required, non-negative integer
- `metadata`: Optional, valid JSON object

### Meeting Webhook Validation
- `booking.id`: Required, unique identifier
- `booking.host_user_id`: Required, must map to existing user
- `booking.start_time`: Required, valid datetime
- `booking.end_time`: Required, valid datetime
- `calendar_event.title`: Required, string

## Swagger Documentation

Interactive API documentation is available at:
```
http://localhost:3001/api (development)
https://your-api-domain.com/api (production)
```

## SDK Examples

### JavaScript/TypeScript
```typescript
const API_BASE = 'http://localhost:3001';

// Create daily update
const createUpdate = async (updateData: DailyUpdate) => {
  const response = await fetch(`${API_BASE}/updates`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${clerkToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(updateData),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create update');
  }
  
  return response.json();
};

// Get user statistics
const getUserStats = async (startDate?: string, endDate?: string) => {
  const params = new URLSearchParams();
  if (startDate) params.append('startDate', startDate);
  if (endDate) params.append('endDate', endDate);
  
  const response = await fetch(`${API_BASE}/updates/my-stats?${params}`, {
    headers: {
      'Authorization': `Bearer ${clerkToken}`,
    },
  });
  
  return response.json();
};
```

### cURL Examples

```bash
# Create daily update
curl -X POST http://localhost:3001/updates \
  -H "Authorization: Bearer YOUR_CLERK_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-01-15",
    "tickets": 12,
    "chats": 8,
    "issues": 5,
    "reviews": 2
  }'

# Get user dashboard
curl -X GET "http://localhost:3001/analytics/dashboard?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer YOUR_CLERK_TOKEN"
```
