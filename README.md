# DailySync - SaaS Dashboard Platform

A comprehensive full-stack SaaS dashboard platform designed for support agents and team members to efficiently log daily updates, track meetings and reviews, and visualize performance metrics with seamless Slack integration and powerful admin-level reporting capabilities.

## 🚀 Live Demo

- **Frontend**: [https://dailysync-demo.vercel.app](https://dailysync-demo.vercel.app) *(Demo URL)*
- **API Documentation**: [https://api.dailysync-demo.com/api](https://api.dailysync-demo.com/api) *(Demo URL)*

## ✨ Key Features

### 📊 Daily Update Submission System
- **Structured Forms**: Intuitive interface for logging daily activities
- **Required Metrics**: Support tickets, live chats, GitHub issues, reviews
- **Flexible Dating**: Auto-default to current date with user editing capability
- **Extensible Architecture**: Custom fields support for future integrations
- **Automatic Slack Posting**: Seamless integration with user's Slack workspace

### 🔗 Individual Slack Integration
- **OAuth 2.0 Flow**: Secure authentication with <PERSON>lack
- **Encrypted Token Storage**: AES-256 encryption for maximum security
- **User-Authenticated Posting**: Messages posted as the actual user, not a bot
- **Formatted Messages**: Clean, professional update format
- **Rate Limiting**: Intelligent throttling to respect Slack API limits

### 📅 Meeting Tracker via Webhook
- **REST Endpoint**: `POST /webhook/meeting` for external calendar systems
- **Automatic User Linking**: Smart mapping of external user IDs
- **Comprehensive Data**: Meeting details, duration, attendees, links
- **Analytics Integration**: Meeting data included in performance dashboards

### 📈 Advanced Analytics Dashboard
- **Personal Dashboard**: Individual performance summaries and trends
- **Admin Dashboard**: Team leaderboards and aggregate metrics
- **Interactive Charts**: Recharts-powered visualizations
- **Flexible Filtering**: Time ranges and metric categories
- **Data Export**: CSV/JSON export functionality

### 🔐 Role-Based Access Control
- **USER**: Submit updates, view personal data only
- **ADMIN**: View team data, access analytics, manage users
- **SUPERADMIN**: Full platform access, user management, system settings

## 🛠 Tech Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for lightning-fast development
- **Styling**: TailwindCSS + shadcn/ui components
- **State Management**: Zustand for global state
- **Charts**: Recharts for interactive data visualization
- **Routing**: React Router v6
- **Authentication**: Clerk React SDK

### Backend
- **Framework**: NestJS with modular architecture
- **Database**: PostgreSQL with Prisma ORM
- **Caching**: Redis for analytics and job queues
- **Authentication**: Clerk for user management and RBAC
- **Integration**: Official Slack SDK
- **Documentation**: Swagger/OpenAPI
- **Validation**: Class-validator and class-transformer

### Infrastructure
- **Frontend Deployment**: Vercel with automatic deployments
- **Backend Deployment**: Railway or Render
- **Database**: PostgreSQL (managed service)
- **Cache**: Redis Cloud
- **Monitoring**: Built-in health checks and error tracking

## 📁 Project Structure

```
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   │   ├── ui/         # shadcn/ui components
│   │   │   ├── forms/      # Form components
│   │   │   └── charts/     # Chart components
│   │   ├── features/       # Feature-specific components
│   │   │   ├── updates/    # Daily updates feature
│   │   │   ├── analytics/  # Analytics feature
│   │   │   ├── meetings/   # Meetings feature
│   │   │   └── slack/      # Slack integration
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility functions
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── store/          # Zustand stores
│   │   └── types/          # TypeScript type definitions
├── backend/                 # NestJS backend API
│   ├── src/
│   │   ├── modules/        # Feature modules
│   │   │   ├── auth/       # Authentication
│   │   │   ├── users/      # User management
│   │   │   ├── updates/    # Daily updates
│   │   │   ├── meetings/   # Meeting management
│   │   │   ├── slack/      # Slack integration
│   │   │   ├── analytics/  # Analytics engine
│   │   │   └── webhooks/   # Webhook handlers
│   │   ├── common/         # Shared utilities
│   │   │   ├── guards/     # Auth guards
│   │   │   ├── decorators/ # Custom decorators
│   │   │   └── pipes/      # Validation pipes
│   │   └── config/         # Configuration
│   └── prisma/             # Database schema and migrations
├── docs/                   # Documentation
├── package.json            # Root workspace configuration
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+ and npm 9+
- **PostgreSQL** database (local or managed)
- **Redis** instance (local or cloud)
- **Clerk** account for authentication
- **Slack** app credentials (for integration)

### Installation

1. **Clone and Install**
```bash
git clone https://github.com/your-username/dailysync.git
cd dailysync
npm install
```

2. **Environment Setup**
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

3. **Configure Environment Variables**

**Backend (.env):**
```env
DATABASE_URL="postgresql://username:password@localhost:5432/dailysync"
REDIS_URL="redis://localhost:6379"
CLERK_SECRET_KEY="your_clerk_secret_key"
SLACK_CLIENT_ID="your_slack_client_id"
SLACK_CLIENT_SECRET="your_slack_client_secret"
ENCRYPTION_KEY="your_32_character_encryption_key"
```

**Frontend (.env):**
```env
VITE_API_URL="http://localhost:3001"
VITE_CLERK_PUBLISHABLE_KEY="your_clerk_publishable_key"
```

4. **Database Setup**
```bash
cd backend
npx prisma generate
npx prisma migrate dev
npx prisma db seed  # Optional: adds sample data
```

5. **Start Development Servers**
```bash
# From root directory
npm run dev
```

This starts both backend (port 3001) and frontend (port 5173) concurrently.

### 🌐 Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api
- **Database Studio**: `npx prisma studio` (from backend directory)

## Environment Variables

### Backend (.env)
```
DATABASE_URL="postgresql://username:password@localhost:5432/dailysync"
REDIS_URL="redis://localhost:6379"
CLERK_SECRET_KEY="your_clerk_secret_key"
SLACK_CLIENT_ID="your_slack_client_id"
SLACK_CLIENT_SECRET="your_slack_client_secret"
ENCRYPTION_KEY="your_32_character_encryption_key"
```

### Frontend (.env)
```
VITE_API_URL="http://localhost:3001"
VITE_CLERK_PUBLISHABLE_KEY="your_clerk_publishable_key"
```

## API Documentation

Once the backend is running, visit `http://localhost:3001/api` for Swagger documentation.

## Database Schema

The application uses the following main entities:
- **User**: User profiles with role-based access
- **Update**: Daily activity submissions
- **Meeting**: Meeting records from webhook integration

## Slack Integration

Users can connect their Slack accounts to automatically post daily updates to designated channels. The integration uses OAuth 2.0 flow and stores encrypted access tokens.

## Meeting Webhook

The platform accepts meeting data via webhook at `POST /webhook/meeting` with automatic user linking and analytics integration.

## Development

### Running Tests
```bash
npm run test
```

### Linting
```bash
npm run lint
```

### Building for Production
```bash
npm run build
```

## Deployment

- **Frontend**: Deploy to Vercel
- **Backend**: Deploy to Railway or Render
- **Database**: PostgreSQL on Railway/Render or managed service
- **Redis**: Redis Cloud or managed service

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is proprietary software. All rights reserved.
