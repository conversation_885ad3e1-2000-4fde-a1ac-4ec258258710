# DailySync - SaaS Dashboard Platform

A full-stack SaaS dashboard platform for support agents and team members to log daily updates, track meetings and reviews, and visualize performance metrics with individual Slack integration and admin-level reporting capabilities.

## Features

- **Daily Update Submission System**: Structured forms for logging daily activities
- **Individual Slack Integration**: OAuth flow for posting updates as authenticated users
- **Meeting Tracker**: Webhook integration for automatic meeting tracking
- **Analytics Dashboard**: Personal and admin-level performance visualization
- **Role-Based Access Control**: USER, ADMIN, and SUPERADMIN roles

## Tech Stack

### Frontend
- React 18 with TypeScript
- Vite for build tooling
- TailwindCSS + shadcn/ui for styling
- Zustand for state management
- Recharts for data visualization
- React Router v6 for routing

### Backend
- NestJS with TypeScript
- PostgreSQL with Prisma ORM
- Redis for caching and job queues
- Clerk for authentication and RBAC
- Slack SDK for integration
- Swagger for API documentation

## Project Structure

```
├── frontend/          # React frontend application
├── backend/           # NestJS backend API
├── package.json       # Root package.json for workspace management
└── README.md         # This file
```

## Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- PostgreSQL database
- Redis instance
- Slack app credentials
- Clerk authentication setup

### Installation

1. Clone the repository and install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

3. Configure your environment variables in both `.env` files

4. Set up the database:
```bash
cd backend
npx prisma migrate dev
npx prisma db seed
```

5. Start the development servers:
```bash
npm run dev
```

This will start both the backend (port 3001) and frontend (port 5173) concurrently.

## Environment Variables

### Backend (.env)
```
DATABASE_URL="postgresql://username:password@localhost:5432/dailysync"
REDIS_URL="redis://localhost:6379"
CLERK_SECRET_KEY="your_clerk_secret_key"
SLACK_CLIENT_ID="your_slack_client_id"
SLACK_CLIENT_SECRET="your_slack_client_secret"
ENCRYPTION_KEY="your_32_character_encryption_key"
```

### Frontend (.env)
```
VITE_API_URL="http://localhost:3001"
VITE_CLERK_PUBLISHABLE_KEY="your_clerk_publishable_key"
```

## API Documentation

Once the backend is running, visit `http://localhost:3001/api` for Swagger documentation.

## Database Schema

The application uses the following main entities:
- **User**: User profiles with role-based access
- **Update**: Daily activity submissions
- **Meeting**: Meeting records from webhook integration

## Slack Integration

Users can connect their Slack accounts to automatically post daily updates to designated channels. The integration uses OAuth 2.0 flow and stores encrypted access tokens.

## Meeting Webhook

The platform accepts meeting data via webhook at `POST /webhook/meeting` with automatic user linking and analytics integration.

## Development

### Running Tests
```bash
npm run test
```

### Linting
```bash
npm run lint
```

### Building for Production
```bash
npm run build
```

## Deployment

- **Frontend**: Deploy to Vercel
- **Backend**: Deploy to Railway or Render
- **Database**: PostgreSQL on Railway/Render or managed service
- **Redis**: Redis Cloud or managed service

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is proprietary software. All rights reserved.
