import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Dashboard from './pages/Dashboard'
import Updates from './pages/Updates'
import Analytics from './pages/Analytics'
import SlackIntegration from './pages/SlackIntegration'
import Layout from './components/Layout'

// Demo mode - bypassing <PERSON> authentication for development
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="updates" element={<Updates />} />
          <Route path="analytics" element={<Analytics />} />
          <Route path="slack" element={<SlackIntegration />} />
        </Route>
      </Routes>
    </Router>
  )
}

export default App
