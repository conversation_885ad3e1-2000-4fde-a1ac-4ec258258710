import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Clerk<PERSON>rovider } from '@clerk/clerk-react'
import Dashboard from './pages/Dashboard'
import Updates from './pages/Updates'
import Analytics from './pages/Analytics'
import SlackIntegration from './pages/SlackIntegration'
import Login from './pages/Login'
import Layout from './components/Layout'

const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key")
}

function App() {
  return (
    <ClerkProvider publishableKey={clerkPub<PERSON><PERSON>}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="updates" element={<Updates />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="slack" element={<SlackIntegration />} />
          </Route>
        </Routes>
      </Router>
    </ClerkProvider>
  )
}

export default App
