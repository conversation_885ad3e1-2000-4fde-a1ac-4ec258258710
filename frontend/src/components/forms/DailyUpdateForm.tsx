import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Calendar, Save } from 'lucide-react'

interface DailyUpdateFormData {
  date: string
  tickets: number
  chats: number
  issues: number
  reviews: number
}

export default function DailyUpdateForm() {
  const [formData, setFormData] = useState<DailyUpdateFormData>({
    date: new Date().toISOString().split('T')[0],
    tickets: 0,
    chats: 0,
    issues: 0,
    reviews: 0,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: keyof DailyUpdateFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // TODO: Submit to API
      console.log('Submitting daily update:', formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Show success message
      alert('Daily update submitted successfully!')
      
      // Reset form for next day
      setFormData(prev => ({
        ...prev,
        tickets: 0,
        chats: 0,
        issues: 0,
        reviews: 0,
      }))
    } catch (error) {
      console.error('Error submitting update:', error)
      alert('Failed to submit update. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Daily Update Submission
        </CardTitle>
        <CardDescription>
          Log your daily activities and performance metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tickets">Support Tickets Replied</Label>
              <Input
                id="tickets"
                type="number"
                min="0"
                value={formData.tickets}
                onChange={(e) => handleInputChange('tickets', parseInt(e.target.value) || 0)}
                placeholder="0"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="chats">Live Chats Handled</Label>
              <Input
                id="chats"
                type="number"
                min="0"
                value={formData.chats}
                onChange={(e) => handleInputChange('chats', parseInt(e.target.value) || 0)}
                placeholder="0"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="issues">GitHub Issues Addressed</Label>
              <Input
                id="issues"
                type="number"
                min="0"
                value={formData.issues}
                onChange={(e) => handleInputChange('issues', parseInt(e.target.value) || 0)}
                placeholder="0"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reviews">Reviews Received</Label>
              <Input
                id="reviews"
                type="number"
                min="0"
                value={formData.reviews}
                onChange={(e) => handleInputChange('reviews', parseInt(e.target.value) || 0)}
                placeholder="0"
                required
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={() => {
              setFormData(prev => ({
                ...prev,
                tickets: 0,
                chats: 0,
                issues: 0,
                reviews: 0,
              }))
            }}>
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Submitting...' : 'Submit Update'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
