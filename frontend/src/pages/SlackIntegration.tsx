import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MessageSquare, CheckCircle, XCircle, Settings, ExternalLink } from 'lucide-react'

export default function SlackIntegration() {
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)

  const handleConnect = async () => {
    setIsConnecting(true)
    try {
      // TODO: Implement Slack OAuth flow
      console.log('Connecting to Slack...')
      
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setIsConnected(true)
      alert('Successfully connected to Slack!')
    } catch (error) {
      console.error('Error connecting to Slack:', error)
      alert('Failed to connect to Slack. Please try again.')
    } finally {
      setIsConnecting(false)
    }
  }

  const handleDisconnect = async () => {
    try {
      // TODO: Implement disconnect logic
      console.log('Disconnecting from Slack...')
      
      setIsConnected(false)
      alert('Disconnected from Slack')
    } catch (error) {
      console.error('Error disconnecting from Slack:', error)
      alert('Failed to disconnect from Slack.')
    }
  }

  const handleTestConnection = async () => {
    try {
      // TODO: Implement test message
      console.log('Testing Slack connection...')
      
      alert('Test message sent to Slack!')
    } catch (error) {
      console.error('Error testing Slack connection:', error)
      alert('Failed to send test message.')
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Slack Integration</h1>
          <p className="text-gray-600">Connect your Slack account to automatically post daily updates</p>
        </div>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Connection Status
          </CardTitle>
          <CardDescription>
            Current status of your Slack integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {isConnected ? (
                <CheckCircle className="h-8 w-8 text-green-500" />
              ) : (
                <XCircle className="h-8 w-8 text-red-500" />
              )}
              <div>
                <p className="font-medium">
                  {isConnected ? 'Connected to Slack' : 'Not Connected'}
                </p>
                <p className="text-sm text-gray-600">
                  {isConnected 
                    ? 'Your daily updates will be automatically posted to Slack'
                    : 'Connect your Slack account to enable automatic posting'
                  }
                </p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              {isConnected ? (
                <>
                  <Button variant="outline" onClick={handleTestConnection}>
                    Test Connection
                  </Button>
                  <Button variant="destructive" onClick={handleDisconnect}>
                    Disconnect
                  </Button>
                </>
              ) : (
                <Button onClick={handleConnect} disabled={isConnecting}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  {isConnecting ? 'Connecting...' : 'Connect to Slack'}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* How it Works */}
      <Card>
        <CardHeader>
          <CardTitle>How It Works</CardTitle>
          <CardDescription>
            Learn about the Slack integration features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium">Automatic Posting</h4>
                <p className="text-sm text-gray-600">
                  When you submit a daily update, it will automatically be posted to your Slack DM as the authenticated user.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium">Formatted Messages</h4>
                <p className="text-sm text-gray-600">
                  Updates are posted in a clean, formatted style showing tickets, chats, issues, and reviews.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium">Secure Integration</h4>
                <p className="text-sm text-gray-600">
                  Your Slack tokens are encrypted and stored securely. You can disconnect at any time.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Message Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Message Preview</CardTitle>
          <CardDescription>
            This is how your daily updates will appear in Slack
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 border rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">JD</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">John Doe</span>
                  <span className="text-sm text-gray-500">2:30 PM</span>
                </div>
                <div className="mt-1 bg-white border rounded p-3">
                  <p className="font-medium">📊 Daily Update - January 15, 2024</p>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>- Tickets: 12</li>
                    <li>- Chats: 8</li>
                    <li>- GitHub Issues: 5</li>
                    <li>- Reviews: 2</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings */}
      {isConnected && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Integration Settings
            </CardTitle>
            <CardDescription>
              Configure your Slack integration preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Auto-post daily updates</p>
                  <p className="text-sm text-gray-600">Automatically post when you submit an update</p>
                </div>
                <Button variant="outline" size="sm">
                  Enabled
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Post to channel</p>
                  <p className="text-sm text-gray-600">Currently posting to your DM</p>
                </div>
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Change
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Message format</p>
                  <p className="text-sm text-gray-600">Standard format with emojis</p>
                </div>
                <Button variant="outline" size="sm">
                  Customize
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
