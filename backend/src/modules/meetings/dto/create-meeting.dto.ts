import { IsString, IsDateString, <PERSON><PERSON>ptional, IsInt, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMeetingDto {
  @ApiProperty()
  @IsString()
  bookingId: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsDateString()
  datetime: string;

  @ApiProperty()
  @IsString()
  timezone: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  meetingLink?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  duration?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  attendeeEmail?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  attendeeName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  metadata?: any;
}
