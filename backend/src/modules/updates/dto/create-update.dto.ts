import { IsInt, IsOptional, IsDateString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateUpdateDto {
  @ApiProperty({ description: 'Date of the update', example: '2024-01-15' })
  @IsDateString()
  date: string;

  @ApiProperty({ description: 'Number of support tickets replied', minimum: 0 })
  @IsInt()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  tickets: number;

  @ApiProperty({ description: 'Number of live chats handled', minimum: 0 })
  @IsInt()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  chats: number;

  @ApiProperty({ description: 'Number of GitHub issues addressed', minimum: 0 })
  @IsInt()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  issues: number;

  @ApiProperty({ description: 'Number of reviews received', minimum: 0 })
  @IsInt()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  reviews: number;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  metadata?: any;
}
