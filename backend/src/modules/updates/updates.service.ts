import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SlackService } from '../slack/slack.service';
import { CreateUpdateDto } from './dto/create-update.dto';
import { UpdateUpdateDto } from './dto/update-update.dto';

@Injectable()
export class UpdatesService {
  constructor(
    private prisma: PrismaService,
    private slackService: SlackService,
  ) {}

  async create(userId: string, createUpdateDto: CreateUpdateDto) {
    const { date, ...updateData } = createUpdateDto;
    const updateDate = new Date(date);

    // Check if update already exists for this date
    const existingUpdate = await this.prisma.update.findUnique({
      where: {
        userId_date: {
          userId,
          date: updateDate,
        },
      },
    });

    if (existingUpdate) {
      throw new ConflictException('Update already exists for this date');
    }

    // Create the update
    const update = await this.prisma.update.create({
      data: {
        userId,
        date: updateDate,
        ...updateData,
      },
      include: {
        user: true,
      },
    });

    // Post to Slack if user has connected their account
    if (update.user.slackToken) {
      try {
        await this.slackService.postDailyUpdate(update.user, update);
      } catch (error) {
        console.error('Failed to post to Slack:', error);
        // Don't fail the update creation if Slack posting fails
      }
    }

    return update;
  }

  async findAll(userId?: string, startDate?: string, endDate?: string) {
    const where: any = {};

    if (userId) {
      where.userId = userId;
    }

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    return this.prisma.update.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const update = await this.prisma.update.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!update) {
      throw new NotFoundException('Update not found');
    }

    return update;
  }

  async update(id: string, updateUpdateDto: UpdateUpdateDto) {
    const existingUpdate = await this.findOne(id);
    
    const { date, ...updateData } = updateUpdateDto;
    const updateDate = date ? new Date(date) : undefined;

    return this.prisma.update.update({
      where: { id },
      data: {
        ...(updateDate && { date: updateDate }),
        ...updateData,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    const update = await this.findOne(id);
    
    return this.prisma.update.delete({
      where: { id },
    });
  }

  async getUserStats(userId: string, startDate?: string, endDate?: string) {
    const where: any = { userId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    const updates = await this.prisma.update.findMany({
      where,
      orderBy: { date: 'desc' },
    });

    const totals = updates.reduce(
      (acc, update) => ({
        tickets: acc.tickets + update.tickets,
        chats: acc.chats + update.chats,
        issues: acc.issues + update.issues,
        reviews: acc.reviews + update.reviews,
      }),
      { tickets: 0, chats: 0, issues: 0, reviews: 0 },
    );

    return {
      updates,
      totals,
      count: updates.length,
    };
  }
}
