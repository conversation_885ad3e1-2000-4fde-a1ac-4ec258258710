import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { UpdatesService } from './updates.service';
import { CreateUpdateDto } from './dto/create-update.dto';
import { UpdateUpdateDto } from './dto/update-update.dto';
import { AuthGuard } from '../auth/auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CurrentUser } from '../auth/user.decorator';
import { Role, User } from '@prisma/client';

@ApiTags('updates')
@ApiBearerAuth()
@Controller('updates')
@UseGuards(AuthGuard)
export class UpdatesController {
  constructor(private readonly updatesService: UpdatesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a daily update' })
  create(@CurrentUser() user: User, @Body() createUpdateDto: CreateUpdateDto) {
    return this.updatesService.create(user.id, createUpdateDto);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Get all updates (Admin only)' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  findAll(
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.updatesService.findAll(userId, startDate, endDate);
  }

  @Get('my-updates')
  @ApiOperation({ summary: 'Get current user updates' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  getMyUpdates(
    @CurrentUser() user: User,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.updatesService.findAll(user.id, startDate, endDate);
  }

  @Get('my-stats')
  @ApiOperation({ summary: 'Get current user statistics' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  getMyStats(
    @CurrentUser() user: User,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.updatesService.getUserStats(user.id, startDate, endDate);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get update by ID' })
  findOne(@Param('id') id: string, @CurrentUser() user: User) {
    return this.updatesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a daily update' })
  update(
    @Param('id') id: string,
    @Body() updateUpdateDto: UpdateUpdateDto,
    @CurrentUser() user: User,
  ) {
    return this.updatesService.update(id, updateUpdateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a daily update' })
  remove(@Param('id') id: string, @CurrentUser() user: User) {
    return this.updatesService.remove(id);
  }
}
