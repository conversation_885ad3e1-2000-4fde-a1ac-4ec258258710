import {
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  Redirect,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { SlackService } from './slack.service';
import { AuthGuard } from '../auth/auth.guard';
import { CurrentUser } from '../auth/user.decorator';
import { User } from '@prisma/client';

@ApiTags('slack')
@ApiBearerAuth()
@Controller('slack')
@UseGuards(AuthGuard)
export class SlackController {
  constructor(private readonly slackService: SlackService) {}

  @Get('oauth-url')
  @ApiOperation({ summary: 'Get Slack OAuth URL' })
  getOAuthUrl() {
    return {
      url: this.slackService.getOAuthUrl(),
    };
  }

  @Get('callback')
  @ApiOperation({ summary: 'Handle Slack OAuth callback' })
  @Redirect()
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @CurrentUser() user: User,
  ) {
    try {
      await this.slackService.handleOAuthCallback(code, user.id);
      return {
        url: `${process.env.FRONTEND_URL}/dashboard?slack=connected`,
      };
    } catch (error) {
      return {
        url: `${process.env.FRONTEND_URL}/dashboard?slack=error`,
      };
    }
  }

  @Get('status')
  @ApiOperation({ summary: 'Check Slack connection status' })
  async getStatus(@CurrentUser() user: User) {
    const isConnected = await this.slackService.testConnection(user.id);
    return { connected: isConnected };
  }

  @Delete('disconnect')
  @ApiOperation({ summary: 'Disconnect Slack integration' })
  async disconnect(@CurrentUser() user: User) {
    await this.slackService.disconnectSlack(user.id);
    return { success: true };
  }

  @Post('test')
  @ApiOperation({ summary: 'Test Slack connection' })
  async testConnection(@CurrentUser() user: User) {
    const isConnected = await this.slackService.testConnection(user.id);
    return { connected: isConnected };
  }
}
