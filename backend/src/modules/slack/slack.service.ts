import { Injectable } from '@nestjs/common';
import { WebClient } from '@slack/web-api';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { UsersService } from '../users/users.service';
import * as crypto from 'crypto';

@Injectable()
export class SlackService {
  private readonly encryptionKey: string;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private usersService: UsersService,
  ) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY');
  }

  private encrypt(text: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  private decrypt(encryptedText: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  getOAuthUrl(): string {
    const clientId = this.configService.get<string>('SLACK_CLIENT_ID');
    const redirectUri = this.configService.get<string>('SLACK_REDIRECT_URI');
    const scopes = 'chat:write,users:read';

    return `https://slack.com/oauth/v2/authorize?client_id=${clientId}&scope=${scopes}&redirect_uri=${encodeURIComponent(redirectUri)}`;
  }

  async handleOAuthCallback(code: string, userId: string) {
    const clientId = this.configService.get<string>('SLACK_CLIENT_ID');
    const clientSecret = this.configService.get<string>('SLACK_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('SLACK_REDIRECT_URI');

    const slack = new WebClient();

    try {
      const result = await slack.oauth.v2.access({
        client_id: clientId,
        client_secret: clientSecret,
        code,
        redirect_uri: redirectUri,
      });

      if (result.ok && result.access_token) {
        const encryptedToken = this.encrypt(result.access_token);
        
        // Get user info from Slack
        const userSlack = new WebClient(result.access_token);
        const userInfo = await userSlack.auth.test();

        await this.usersService.updateSlackToken(
          userId,
          encryptedToken,
          userInfo.user_id as string,
        );

        // Send welcome message
        await this.sendWelcomeMessage(result.access_token);

        return { success: true };
      }

      throw new Error('OAuth failed');
    } catch (error) {
      console.error('Slack OAuth error:', error);
      throw error;
    }
  }

  async sendWelcomeMessage(accessToken: string) {
    const slack = new WebClient(accessToken);

    try {
      await slack.chat.postMessage({
        channel: '@me', // DM to the user
        text: '🎉 Welcome to DailySync! Your Slack integration is now active. Your daily updates will be posted here automatically.',
      });
    } catch (error) {
      console.error('Failed to send welcome message:', error);
    }
  }

  async postDailyUpdate(user: any, update: any) {
    if (!user.slackToken) {
      throw new Error('User has not connected Slack');
    }

    const decryptedToken = this.decrypt(user.slackToken);
    const slack = new WebClient(decryptedToken);

    const date = new Date(update.date).toLocaleDateString();
    const message = `📊 Daily Update - ${date}
- Tickets: ${update.tickets}
- Chats: ${update.chats}
- GitHub Issues: ${update.issues}
- Reviews: ${update.reviews}`;

    try {
      await slack.chat.postMessage({
        channel: '@me', // DM to the user
        text: message,
      });
    } catch (error) {
      console.error('Failed to post daily update to Slack:', error);
      throw error;
    }
  }

  async disconnectSlack(userId: string) {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        slackToken: null,
        slackUserId: null,
      },
    });
  }

  async testConnection(userId: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user?.slackToken) {
      return false;
    }

    try {
      const decryptedToken = this.decrypt(user.slackToken);
      const slack = new WebClient(decryptedToken);
      const result = await slack.auth.test();
      return result.ok;
    } catch (error) {
      console.error('Slack connection test failed:', error);
      return false;
    }
  }
}
