// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String    @id @default(cuid())
  clerkId     String    @unique
  name        String
  email       String    @unique
  role        Role      @default(USER)
  slackToken  String?   // encrypted storage
  slackUserId String?
  updates     Update[]
  meetings    Meeting[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("users")
}

model Update {
  id        String   @id @default(cuid())
  userId    String
  date      DateTime @db.Date
  tickets   Int      @default(0)
  chats     Int      @default(0)
  issues    Int      @default(0)
  reviews   Int      @default(0)
  metadata  Json?    // for extensible custom fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([userId, date]) // prevent duplicate entries per day
  @@map("updates")
}

model Meeting {
  id          String   @id @default(cuid())
  userId      String
  bookingId   String   @unique // external booking ID
  title       String
  datetime    DateTime
  timezone    String
  meetingLink String?
  duration    Int?     // minutes
  attendeeEmail String?
  attendeeName  String?
  status      String   @default("scheduled")
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("meetings")
}

enum Role {
  USER
  ADMIN
  SUPERADMIN
}
