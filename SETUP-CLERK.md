# Setting Up Clerk Authentication for DailySync

This guide will help you set up Clerk authentication to replace the demo mode.

## Step 1: Create a Clerk Account

1. Go to [https://clerk.com](https://clerk.com)
2. Sign up for a free account
3. Create a new application
4. Choose your preferred authentication methods (email, Google, GitHub, etc.)

## Step 2: Get Your API Keys

1. In your Clerk dashboard, go to **API Keys**
2. Copy the **Publishable Key** (starts with `pk_test_` or `pk_live_`)
3. Copy the **Secret Key** (starts with `sk_test_` or `sk_live_`)

## Step 3: Update Environment Variables

### Frontend (.env)
```env
VITE_API_URL="http://localhost:3001"
VITE_CLERK_PUBLISHABLE_KEY="pk_test_your_actual_publishable_key_here"
```

### Backend (.env)
```env
# ... other variables ...
CLERK_SECRET_KEY="sk_test_your_actual_secret_key_here"
```

## Step 4: Restore Clerk Integration

### Update App.tsx
Replace the demo version with the full Clerk integration:

```tsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ClerkProvider } from '@clerk/clerk-react'
import Dashboard from './pages/Dashboard'
import Updates from './pages/Updates'
import Analytics from './pages/Analytics'
import SlackIntegration from './pages/SlackIntegration'
import Login from './pages/Login'
import Layout from './components/Layout'

const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key")
}

function App() {
  return (
    <ClerkProvider publishableKey={clerkPubKey}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="updates" element={<Updates />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="slack" element={<SlackIntegration />} />
          </Route>
        </Routes>
      </Router>
    </ClerkProvider>
  )
}

export default App
```

### Update Layout.tsx
```tsx
import { Outlet } from 'react-router-dom'
import { useAuth } from '@clerk/clerk-react'
import { Navigate } from 'react-router-dom'
import Sidebar from './Sidebar'
import Header from './Header'

export default function Layout() {
  const { isSignedIn, isLoaded } = useAuth()

  if (!isLoaded) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (!isSignedIn) {
    return <Navigate to="/login" replace />
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          <Outlet />
        </main>
      </div>
    </div>
  )
}
```

### Update Header.tsx
```tsx
import { UserButton } from '@clerk/clerk-react'
import { Bell, Search } from 'lucide-react'
import { Button } from './ui/button'

export default function Header() {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">DailySync</h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </Button>
          
          <UserButton afterSignOutUrl="/login" />
        </div>
      </div>
    </header>
  )
}
```

## Step 5: Configure User Roles (Optional)

1. In Clerk dashboard, go to **Users & Authentication** > **Metadata**
2. Add custom metadata for user roles:
   ```json
   {
     "role": "USER"
   }
   ```
3. Available roles: `USER`, `ADMIN`, `SUPERADMIN`

## Step 6: Set Up Webhooks (Optional)

To sync users between Clerk and your database:

1. In Clerk dashboard, go to **Webhooks**
2. Add endpoint: `https://your-api-domain.com/webhooks/clerk`
3. Select events: `user.created`, `user.updated`, `user.deleted`
4. Add the webhook secret to your backend environment

## Step 7: Test the Integration

1. Restart your development servers:
   ```bash
   npm run dev
   ```

2. Visit `http://localhost:5173`
3. You should be redirected to the Clerk sign-in page
4. Create an account or sign in
5. You should be redirected to the dashboard

## Troubleshooting

### Common Issues

1. **Invalid publishable key error**
   - Make sure you copied the key correctly
   - Ensure there are no extra spaces or characters
   - Check that the key starts with `pk_test_` or `pk_live_`

2. **CORS errors**
   - Make sure your backend CORS configuration includes your frontend URL
   - Check that the API URL in frontend/.env is correct

3. **User not found in database**
   - Set up the Clerk webhook to automatically create users
   - Or manually create users in your database with matching Clerk IDs

### Development vs Production

- **Development**: Use `pk_test_` and `sk_test_` keys
- **Production**: Use `pk_live_` and `sk_live_` keys
- Make sure to update environment variables when deploying

## Next Steps

After setting up Clerk:

1. Set up the PostgreSQL database
2. Run database migrations
3. Configure Slack integration
4. Deploy to production

For more details, see the main README.md and DEPLOYMENT.md files.
