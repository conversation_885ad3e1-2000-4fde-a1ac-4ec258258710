# DailySync Deployment Guide

This guide covers how to deploy the DailySync application to production.

## Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL database
- Redis instance
- Clerk account for authentication
- Slack app credentials (for integration)

## Environment Setup

### Backend Environment Variables

Create a `.env` file in the `backend` directory:

```env
# Database
DATABASE_URL="****************************************/dailysync"

# Redis
REDIS_URL="redis://host:6379"

# Clerk Authentication
CLERK_SECRET_KEY="sk_live_your_clerk_secret_key"

# Slack Integration
SLACK_CLIENT_ID="your_slack_client_id"
SLACK_CLIENT_SECRET="your_slack_client_secret"
SLACK_REDIRECT_URI="https://your-domain.com/auth/slack/callback"

# Encryption
ENCRYPTION_KEY="your_32_character_encryption_key_here"

# Application
PORT=3001
NODE_ENV="production"

# CORS
FRONTEND_URL="https://your-frontend-domain.com"
```

### Frontend Environment Variables

Create a `.env` file in the `frontend` directory:

```env
# API Configuration
VITE_API_URL="https://your-backend-domain.com"

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY="pk_live_your_clerk_publishable_key"
```

## Database Setup

1. Create a PostgreSQL database
2. Run database migrations:

```bash
cd backend
npm run db:migrate
```

3. (Optional) Seed with sample data:

```bash
npm run db:seed
```

## Deployment Options

### Option 1: Railway (Recommended)

#### Backend Deployment

1. Connect your GitHub repository to Railway
2. Create a new service for the backend
3. Set the root directory to `backend`
4. Add environment variables in Railway dashboard
5. Railway will automatically detect and deploy the NestJS app

#### Frontend Deployment

1. Deploy frontend to Vercel:

```bash
cd frontend
npm run build
npx vercel --prod
```

2. Set environment variables in Vercel dashboard

### Option 2: Docker Deployment

#### Backend Dockerfile

Create `backend/Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start:prod"]
```

#### Frontend Dockerfile

Create `frontend/Dockerfile`:

```dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: dailysync
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      DATABASE_URL: ********************************************/dailysync
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

## Clerk Setup

1. Create a Clerk application at https://clerk.com
2. Configure OAuth providers if needed
3. Set up user roles (USER, ADMIN, SUPERADMIN)
4. Add webhook endpoints for user creation
5. Copy the publishable and secret keys to your environment variables

## Slack App Setup

1. Create a Slack app at https://api.slack.com/apps
2. Configure OAuth scopes: `chat:write`, `users:read`
3. Set redirect URL to your backend callback endpoint
4. Copy Client ID and Client Secret to environment variables

## Production Checklist

### Security
- [ ] Use HTTPS for all endpoints
- [ ] Set secure CORS origins
- [ ] Use strong encryption keys
- [ ] Enable rate limiting
- [ ] Set up proper authentication

### Performance
- [ ] Enable Redis caching
- [ ] Optimize database queries
- [ ] Set up CDN for frontend assets
- [ ] Enable gzip compression
- [ ] Monitor application performance

### Monitoring
- [ ] Set up error tracking (Sentry)
- [ ] Configure logging
- [ ] Set up health checks
- [ ] Monitor database performance
- [ ] Set up alerts for critical issues

### Backup
- [ ] Set up automated database backups
- [ ] Test backup restoration
- [ ] Document recovery procedures

## Scaling Considerations

### Database
- Use connection pooling
- Consider read replicas for heavy read workloads
- Implement database indexing for performance

### Backend
- Use horizontal scaling with load balancers
- Implement caching strategies
- Consider microservices architecture for large teams

### Frontend
- Use CDN for static assets
- Implement code splitting
- Optimize bundle sizes

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor security vulnerabilities
- Review and rotate encryption keys
- Clean up old data periodically

### Monitoring
- Set up uptime monitoring
- Monitor API response times
- Track user engagement metrics
- Monitor database performance

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify database server is running
   - Check firewall settings

2. **Slack Integration Issues**
   - Verify OAuth redirect URLs
   - Check Slack app permissions
   - Validate client credentials

3. **Authentication Issues**
   - Verify Clerk configuration
   - Check JWT token validation
   - Validate environment variables

### Logs

Check application logs for detailed error information:

```bash
# Backend logs
docker logs dailysync-backend

# Frontend logs (if using Docker)
docker logs dailysync-frontend
```

## Support

For deployment issues:
1. Check the logs for error details
2. Verify all environment variables are set correctly
3. Ensure all services are running and accessible
4. Check network connectivity between services
